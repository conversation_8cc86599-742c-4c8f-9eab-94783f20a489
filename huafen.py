import os, shutil
from sklearn.model_selection import train_test_split

# 添加错误处理装饰器
def try_except(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"错误: {e}")
    return wrapper

@try_except
def main():
    val_size = 0.2
    postfix = 'png'
    imgpath = r'C:\Users\<USER>\Desktop\dataset\images'
    txtpath = r'C:\Users\<USER>\Desktop\dataset\labels'

    # 检查源路径是否存在
    if not os.path.exists(imgpath):
        raise FileNotFoundError(f"图像路径不存在: {imgpath}")
    if not os.path.exists(txtpath):
        raise FileNotFoundError(f"标签路径不存在: {txtpath}")

    output_train_img_folder = r'E:\pyProject\yolov11\dataset/images/train'
    output_val_img_folder = r'E:\pyProject\yolov11\dataset/images/val'
    output_train_txt_folder = r'E:\pyProject\yolov11\dataset\labels/train'
    output_val_txt_folder = r'E:\pyProject\yolov11\dataset\labels/val'

    os.makedirs(output_train_img_folder, exist_ok=True)
    os.makedirs(output_val_img_folder, exist_ok=True)
    os.makedirs(output_train_txt_folder, exist_ok=True)
    os.makedirs(output_val_txt_folder, exist_ok=True)

    listdir = [i for i in os.listdir(txtpath) if i.endswith('.txt')]
    if not listdir:
        raise ValueError(f"在 {txtpath} 中没有找到txt文件")
    
    train, val = train_test_split(listdir, test_size=val_size, shuffle=True, random_state=0)
    print(f"分割数据集: 训练集 {len(train)} 文件, 验证集 {len(val)} 文件")

    # 复制训练集文件
    for i in train:
        img_source_path = os.path.join(imgpath, f"{i[:-4]}.{postfix}")
        txt_source_path = os.path.join(txtpath, i)

        # 检查源文件是否存在
        if not os.path.exists(img_source_path):
            print(f"警告: 图像文件不存在: {img_source_path}")
            continue
        if not os.path.exists(txt_source_path):
            print(f"警告: 标签文件不存在: {txt_source_path}")
            continue

        img_destination_path = os.path.join(output_train_img_folder, f"{i[:-4]}.{postfix}")
        txt_destination_path = os.path.join(output_train_txt_folder, i)

        shutil.copy(img_source_path, img_destination_path)
        shutil.copy(txt_source_path, txt_destination_path)

    # 复制验证集文件
    for i in val:
        img_source_path = os.path.join(imgpath, f"{i[:-4]}.{postfix}")
        txt_source_path = os.path.join(txtpath, i)

        # 检查源文件是否存在
        if not os.path.exists(img_source_path):
            print(f"警告: 图像文件不存在: {img_source_path}")
            continue
        if not os.path.exists(txt_source_path):
            print(f"警告: 标签文件不存在: {txt_source_path}")
            continue

        img_destination_path = os.path.join(output_val_img_folder, f"{i[:-4]}.{postfix}")
        txt_destination_path = os.path.join(output_val_txt_folder, i)

        shutil.copy(img_source_path, img_destination_path)
        shutil.copy(txt_source_path, txt_destination_path)

    print("数据集分割完成!")

if __name__ == "__main__":
    main()